%%capture --no-stderr
%pip install --quiet -U langchain_openai langchain_core langchain_community tavily-python

import os, getpass

def _set_env(var: str):
    if not os.environ.get(var):
        os.environ[var] = getpass.getpass(f"{var}: ")

_set_env("OPENAI_API_KEY")

import os, getpass

def _set_env(var: str):
    if not os.environ.get(var):
        os.environ[var] = getpass.getpass(f"{var}: ")

_set_env("PPLX_API_KEY")

from langchain_openai import ChatOpenAI
gpt4o_chat = ChatOpenAI(model="gpt-4o", temperature=0)
gpt35_chat = ChatOpenAI(model="gpt-3.5-turbo-0125", temperature=0)

from langchain_perplexity import ChatPerplexity

ppx_chat = ChatPerplexity(
    model="sonar",   # any available PPLX model
    temperature=0,
)


from langchain_core.messages import HumanMessage

# Create a message
msg = HumanMessage(content="Hello world", name="Lance")

# Message list
messages = [msg]

# Invoke the model with a list of messages 
ppx_chat.invoke(messages)

ppx_chat.invoke("hello world")

gpt35_chat.invoke("hello world")

_set_env("TAVILY_API_KEY")

from langchain_community.tools.tavily_search import TavilySearchResults
tavily_search = TavilySearchResults(max_results=3)
search_docs = tavily_search.invoke("What is LangGraph?")

search_docs

