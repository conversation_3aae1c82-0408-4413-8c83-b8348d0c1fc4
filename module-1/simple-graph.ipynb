{"cells": [{"cell_type": "markdown", "id": "8d5f3703", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-1/simple-graph.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58238187-lesson-2-simple-graph)"]}, {"attachments": {}, "cell_type": "markdown", "id": "50fa7f8a-8764-4bb9-9968-48b681a0e4f1", "metadata": {}, "source": ["# The Simplest Graph\n", "\n", "Let's build a simple graph with 3 nodes and one conditional edge. \n", "\n", "![Screenshot 2024-08-20 at 3.11.22 PM.png](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66dba5f465f6e9a2482ad935_simple-graph1.png)"]}, {"cell_type": "code", "execution_count": 1, "id": "ff151ef1-fa30-482a-94da-8f49964afbc3", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph"]}, {"cell_type": "markdown", "id": "5999f8d0-989f-4638-8ade-5c257cbadfe8", "metadata": {}, "source": ["## State\n", "\n", "First, define the [State](https://langchain-ai.github.io/langgraph/concepts/low_level/#state) of the graph. \n", "\n", "The State schema serves as the input schema for all Nodes and Edges in the graph.\n", "\n", "Let's use the `TypedDict` class from python's `typing` module as our schema, which provides type hints for the keys."]}, {"cell_type": "code", "execution_count": 3, "id": "6a90709b-ddfa-4671-8acc-c59969a29991", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "\n", "class State(TypedDict):\n", "    graph_state: str"]}, {"cell_type": "markdown", "id": "888509e1-cbde-4c03-99a0-2560dd2e262d", "metadata": {}, "source": ["## Nodes\n", "\n", "[Nodes](https://langchain-ai.github.io/langgraph/concepts/low_level/#nodes) are just python functions.\n", "\n", "The first positional argument is the state, as defined above.\n", "\n", "Because the state is a `TypedDict` with schema as defined above, each node can access the key, `graph_state`, with `state['graph_state']`.\n", "\n", "Each node returns a new value of the state key `graph_state`.\n", "  \n", "By default, the new value returned by each node [will override](https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers) the prior state value."]}, {"cell_type": "code", "execution_count": 4, "id": "e8aabcb7-494c-4d35-be08-f81c76d75a6b", "metadata": {}, "outputs": [], "source": ["def node_1(state):\n", "    print(\"---Node 1---\")\n", "    return {\"graph_state\": state['graph_state'] +\" I am\"}\n", "\n", "def node_2(state):\n", "    print(\"---Node 2---\")\n", "    return {\"graph_state\": state['graph_state'] +\" happy!\"}\n", "\n", "def node_3(state):\n", "    print(\"---Node 3---\")\n", "    return {\"graph_state\": state['graph_state'] +\" sad!\"}"]}, {"cell_type": "markdown", "id": "ad056608-8c8f-4999-bb53-10583efa4ed8", "metadata": {}, "source": ["## Edges\n", "\n", "[Edges](https://langchain-ai.github.io/langgraph/concepts/low_level/#edges) connect the nodes.\n", "\n", "Normal Edges are used if you want to *always* go from, for example, `node_1` to `node_2`.\n", "\n", "[Conditional Edges](https://langchain-ai.github.io/langgraph/concepts/low_level/#conditional-edges) are used if you want to *optionally* route between nodes.\n", " \n", "Conditional edges are implemented as functions that return the next node to visit based upon some logic."]}, {"cell_type": "code", "execution_count": 5, "id": "7e53543a-902a-4d41-ad3d-25eee260e819", "metadata": {}, "outputs": [], "source": ["import random\n", "from typing import Literal\n", "\n", "def decide_mood(state) -> Literal[\"node_2\", \"node_3\"]:\n", "    \n", "    # Often, we will use state to decide on the next node to visit\n", "    user_input = state['graph_state'] \n", "    \n", "    # Here, let's just do a 50 / 50 split between nodes 2, 3\n", "    if random.random() < 0.5:\n", "\n", "        # 50% of the time, we return Node 2\n", "        return \"node_2\"\n", "    \n", "    # 50% of the time, we return Node 3\n", "    return \"node_3\""]}, {"cell_type": "markdown", "id": "9282ea7a-5ed2-4641-bed8-c3472d54c951", "metadata": {}, "source": ["## Graph Construction\n", "\n", "Now, we build the graph from our [components](\n", "https://langchain-ai.github.io/langgraph/concepts/low_level/) defined above.\n", "\n", "The [StateGraph class](https://langchain-ai.github.io/langgraph/concepts/low_level/#stategraph) is the graph class that we can use.\n", " \n", "First, we initialize a StateGraph with the `State` class we defined above.\n", " \n", "Then, we add our nodes and edges.\n", "\n", "We use the [`START` Node, a special node](https://langchain-ai.github.io/langgraph/concepts/low_level/#start-node) that sends user input to the graph, to indicate where to start our graph.\n", " \n", "The [`END` Node](https://langchain-ai.github.io/langgraph/concepts/low_level/#end-node) is a special node that represents a terminal node. \n", "\n", "Finally, we [compile our graph](https://langchain-ai.github.io/langgraph/concepts/low_level/#compiling-your-graph) to perform a few basic checks on the graph structure. \n", "\n", "We can visualize the graph as a [Mermaid diagram](https://github.com/mermaid-js/mermaid)."]}, {"cell_type": "code", "execution_count": 6, "id": "7deb0359-55c1-4545-b52e-8252994befbb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "# Build graph\n", "builder = StateGraph(State)\n", "builder.add_node(\"node_1\", node_1)\n", "builder.add_node(\"node_2\", node_2)\n", "builder.add_node(\"node_3\", node_3)\n", "\n", "# Logic\n", "builder.add_edge(START, \"node_1\")\n", "builder.add_conditional_edges(\"node_1\", decide_mood)\n", "builder.add_edge(\"node_2\", END)\n", "builder.add_edge(\"node_3\", END)\n", "\n", "# Add\n", "graph = builder.compile()\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "00617c74-2647-44ea-8a2e-310dd96c0d26", "metadata": {}, "source": ["## Graph Invocation\n", "\n", "The compiled graph implements the [runnable](https://python.langchain.com/docs/concepts/runnables/) protocol.\n", "\n", "This provides a standard way to execute LangChain components. \n", " \n", "`invoke` is one of the standard methods in this interface.\n", "\n", "The input is a dictionary `{\"graph_state\": \"Hi, this is lance.\"}`, which sets the initial value for our graph state dict.\n", "\n", "When `invoke` is called, the graph starts execution from the `START` node.\n", "\n", "It progresses through the defined nodes (`node_1`, `node_2`, `node_3`) in order.\n", "\n", "The conditional edge will traverse from node `1` to node `2` or `3` using a 50/50 decision rule. \n", "\n", "Each node function receives the current state and returns a new value, which overrides the graph state.\n", "\n", "The execution continues until it reaches the `END` node."]}, {"cell_type": "code", "execution_count": 14, "id": "e895f17a-e835-4e8a-8e1b-63fe6d27cc52", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Node 1---\n", "---Node 2---\n"]}, {"data": {"text/plain": ["{'graph_state': 'Hi, this is <PERSON><PERSON>. I am happy!'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"graph_state\" : \"Hi, this is <PERSON><PERSON>.\"})"]}, {"cell_type": "markdown", "id": "082399c3-18bd-4b67-97c1-2005f268abc5", "metadata": {}, "source": ["`invoke` runs the entire graph synchronously.\n", "\n", "This waits for each step to complete before moving to the next.\n", "\n", "It returns the final state of the graph after all nodes have executed.\n", "\n", "In this case, it returns the state after `node_3` has completed: \n", "\n", "```\n", "{'graph_state': '<PERSON>, this is <PERSON>. I am sad!'}\n", "```"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}