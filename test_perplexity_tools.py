#!/usr/bin/env python3
"""
Test script to verify that ChatPerplexity bind_tools works
"""

import os
from langchain_perplexity import ChatPerplexity
from langchain_core.messages import HumanMessage

def multiply(a: int, b: int) -> int:
    """Multiply a and b.

    Args:
        a: first int
        b: second int
    """
    return a * b

def main():
    # Check if API key is set
    if not os.environ.get("PPLX_API_KEY"):
        print("❌ PPLX_API_KEY environment variable not set")
        return
    
    try:
        # Create ChatPerplexity instance
        llm = ChatPerplexity(model="sonar")
        print("✅ ChatPerplexity instance created successfully")
        
        # Try to bind tools
        llm_with_tools = llm.bind_tools([multiply])
        print("✅ bind_tools() worked! No NotImplementedError")
        
        # Test a simple tool call
        response = llm_with_tools.invoke([
            HumanMessage(content="What is 2 multiplied by 3?")
        ])
        
        print("✅ Tool call successful!")
        print(f"Response type: {type(response)}")
        
        if hasattr(response, 'tool_calls') and response.tool_calls:
            print(f"Tool calls found: {response.tool_calls}")
        else:
            print("No tool calls in response (model may have chosen not to use tools)")
            print(f"Response content: {response.content}")
            
    except NotImplementedError:
        print("❌ bind_tools() still raises NotImplementedError")
    except Exception as e:
        print(f"❌ Other error: {e}")

if __name__ == "__main__":
    main()
